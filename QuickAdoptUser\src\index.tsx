import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import reportWebVitals from "./reportWebVitals";

const banner = document.createElement("div");
banner.id = "quickAdopt_banner";
// //banner.style.position = 'fixed';
banner.style.top = '0';
banner.style.left = '0';
banner.style.width = '100% !important';
const body = document.querySelector('body') as HTMLElement;
body.appendChild(banner);
const root = ReactDOM.createRoot(banner as HTMLElement);
function captureUserDetails(userDetails: any) {
    //console.log("Captured User Details:", userDetails);
}
//Expose capture Details globally 
(window as any).captureUserDetails = (userDetails: any) => {
    if (!userDetails) {
        console.error("No user details provided.");
        return;
    }
	//console.log("User details captured:", userDetails);
	localStorage.setItem("userStats",JSON.stringify(userDetails))
};
// Explose globally to capture account Id
    let accountId = (window as any).AccountId 
    if (accountId === '') {
        console.error("AccountId not provided.");      
    }
    else {
        //console.log("Account Id :", accountId);
	    localStorage.setItem("AccountId",accountId)
}
    
let domainAccountId = (window as any).DomainAccountId;
    if (domainAccountId === '') {
        console.error("Domain AccountId not provided.");      
    }
    else {
        //console.log("Account Id :", accountId);
	    localStorage.setItem("DomainAccountId",domainAccountId)
}
    
let modelSelected = (window as any).ChatDisplayMode;
    if (modelSelected === ''|| modelSelected===undefined||modelSelected==="undefined") {
        console.log("Mode is Set To Deafult Side Panel");  
        localStorage.setItem("chatDisplayMode", "sidePanel");
    }
    else {
        //console.log("Account Id :", accountId);
        localStorage.setItem("chatDisplayMode", modelSelected);
    }
	


root.render(
    <App />
);


// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(//console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
