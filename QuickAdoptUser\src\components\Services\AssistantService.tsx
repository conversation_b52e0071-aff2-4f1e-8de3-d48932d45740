import { apiTime } from "../../service/APIService";
import { adminApiService, userApiService } from "./APIservice";

// Interface for the assistant response
export interface AssistantResponse {
  ResponseType: string;
  Message: string;
  ChatHistoryId?: string;
  Guide?: any; // Optional Guide property for guide responses
  AudioBase64?: string;
}

// Interface for message handlers
export interface MessageHandlers {
  onMessage?: (message: AssistantResponse) => void;
  onError?: (error: any) => void;
  onComplete?: () => void;
}
export interface TTSChunk {
  index: number;
  base64: string;
}


/**
 * Send a message to the assistant using SSE (Server-Sent Events)
 * @param message The message to send
 * @param handlers Callback handlers for message events
 */
export const SendMessageSSE = async (message: string, handlers: MessageHandlers) => {
  try {
    const threadId = localStorage.getItem("ThreadId") || "";
    const accountId = localStorage.getItem("AccountId") || "";
    const apiUrl = process.env.REACT_APP_USER_API || '';
    const chatHistoryId = localStorage.getItem("chatHistoryId");

    // Make the fetch request
    const response = await fetch(`${apiUrl}/Assistant/GetResponseFromAssistant`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        message: message,
        threadId: threadId,
        accountId: accountId,
        chatHistoryId: chatHistoryId || null
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    if (!response.body) {
      throw new Error("Response body is null");
    }

    // Get the reader from the response body
    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let chunk = "";

    //console.log('AssistantService: Started reading response stream');

    // Process the stream
    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        //console.log('AssistantService: Stream reading complete');
        if (handlers.onComplete) {
          handlers.onComplete();
        }
        break;
      }

      // Decode the chunk and add to buffer
      chunk += decoder.decode(value, { stream: true });

      // Process complete lines
      const lines = chunk.split("\n\n");
      for (let i = 0; i < lines.length - 1; i++) {
        const line = lines[i].replace(/^data: /, "");

        //console.log('AssistantService: Received line:', line);

        // Check if this is the end marker
        if (line === "[[DONE]]") {
          //console.log('AssistantService: Stream finished');
          // const audio = new Audio('http://minio-web.quickadopt.in/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3F1aWNrYWRvcHRkZXYvbWVzc2dhZVJlY2V2aWVkLm1wMz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPTA0WjI3VkdRTE9ORjhQQTNNWlQ1JTJGMjAyNTA1MTUlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjUwNTE1VDEwMDc0OFomWC1BbXotRXhwaXJlcz00MzIwMCZYLUFtei1TZWN1cml0eS1Ub2tlbj1leUpoYkdjaU9pSklVelV4TWlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKaFkyTmxjM05MWlhraU9pSXdORm95TjFaSFVVeFBUa1k0VUVFelRWcFVOU0lzSW1WNGNDSTZNVGMwTnpNME5qWTBNaXdpY0dGeVpXNTBJam9pYldsdWFXOW5kV1Z6ZENKOS5oTDl1cFItZUllSHF0VVdxVk9UWm9oU0RqQWJSRlVZejd1WUYwclA5Q2xHUUN6TUpfWm5kOFlhMHUyTUMwdkVPS3lPTV9HdnNMUkJ3WVpsRTJfaHExUSZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QmdmVyc2lvbklkPW51bGwmWC1BbXotU2lnbmF0dXJlPTdlYWI0ODQ2Y2Y2MTA4YTY0Y2VmNWMwMzU2YTQ3NTFhNzBiZmQ3MWNkODA5ZDYyZWU4YTlmYzBkZGIwMzExMmI');

          // audio.play().catch(err => console.warn('Sound play failed:', err));


  
          if (handlers.onComplete) {
            handlers.onComplete();
          }
          return;
        }

        try {
          // Try to parse the line as JSON
          const parsed: AssistantResponse = JSON.parse(line);

          // Call the message handler if provided
          if (handlers.onMessage) {
            handlers.onMessage(parsed);
          }
        } catch (e) {
          console.warn("AssistantService: Failed to parse message as JSON:", line);

          // If we can't parse as JSON, create a simple message object
          if (handlers.onMessage) {
            const simpleResponse: AssistantResponse = {
              ResponseType: "message",
              Message: line,
              
            };
            handlers.onMessage(simpleResponse);
          }
        }
      }

      // Keep the last incomplete line
      chunk = lines[lines.length - 1];
    }
  } catch (error) {
    console.error('AssistantService: Error processing response:', error);
    if (handlers.onError) {
      handlers.onError(error);
    }
  }
};

// Legacy method for backward compatibility
export const SendMessage = async (message: any) => {
	try {
        const response = await userApiService.post(`/Assistant/GetResponseFromAssistant`, {
            Message: message,
			ThreadId: localStorage.getItem("ThreadId"),
			AccountId : localStorage.getItem("AccountId"),
        });
        //localStorage.getItem("ThreadId"),
		if (response) {
			return response.data
		} else {
			console.error("Failed to update guide");
		}
	} catch (error) {
		console.error("Error update guide:", error);
	} finally {
	}
};

export const createNewThread = async () => {
	try {
		const response = await userApiService.post("/Assistant/NewChat");
		if (response && response.data?.threadId) {
			localStorage.setItem("ThreadId", response.data.threadId);
			return response.data.threadId;
		} else {

		}
	} catch (error) {

	}
};

export const speakWithOpenAITTS = async (text: string) => {
  try {
    const response = await userApiService.post("/Assistant/StreamAudio/stream", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ text })
    });

    if (!response) {
      throw new Error("Failed to fetch audio from server");
    }

    const blob = await response.data.blob();
    
    const audioUrl = URL.createObjectURL(blob);
    
    const audio = new Audio(audioUrl);
    
    audio.play();
    
  } catch (err) {
    console.error("OpenAI TTS Error:", err);
  }
};


export const FetchWelcomeAudio = async (text: string) => {
  try {
    const res = await userApiService.post("/Assistant/GenerateAudio", {
      
       Text: text,
    });

    const data = await res.data;

   return data || null;
    }
   catch (err) {
    console.error("Error fetching welcome audio:", err);
  }
};
export const streamWelcomeAudio = async (text: string): Promise<ReadableStream<Uint8Array> | null> => {
  try {
    const res = await userApiService.post("/Assistant/StreamAudio", {
      
      Text: text,
   });

   const data = await res.data;

  return data || null;
  } catch (err) {
    console.error("Error streaming welcome audio:", err);
    return null;
  }
};
// services/ttsService.ts

export const fetchTTSStream = async (
  text: string,
  signal: AbortSignal
): Promise<ReadableStream<Uint8Array>> => {
  const token = localStorage.getItem("accessToken");
  const userapi = process.env.REACT_APP_USER_API || '';
  const response = await fetch(`${userapi}/Assistant/StreamTTSChunks`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${token}`,
    },
    body: JSON.stringify({ text }),
    signal,
  });

  if (!response.body) {
    throw new Error("ReadableStream not supported");
  }

  return response.body;
};

export const IsOpenAIKeyEnabledForAccount = async (openSnackbar:any,accountId: any,setIsOpenAIKeyProvided: any) => {
  try {
  const response = await userApiService.get(`/Assistant/IsOpenAIKeyProvided?accountId=${accountId}` );
      let apiData = response.data;
      if (apiData) {
          setIsOpenAIKeyProvided("Provided");
      } else {
          setIsOpenAIKeyProvided("Not Provided");
          openSnackbar("API key not provided,please contact admin", "error");
      }
} catch (error) {
      throw error;
} finally {

}
};

export const IsDonnaEnabled = async (accountId: any, domainAccountId: any, setEnableDona: any) => {
  try {
    const response = await userApiService.get("/Assistant/IsAssistantEnabled", {
      params: {
        accountId: "********-*********-03a53589-7d9d-4ca3-9928-3fa52dfffe7e",// accountId,
        domainAccountId: domainAccountId,
      },
    });

    if (response && response.data) {
      if (response.data === true || response.data === "true") {
        // Do something, e.g., enable Donna
        setEnableDona(true);
      } else {
        setEnableDona(false);
      }
    } else {
      setEnableDona(false);
    }
  } catch (error) {
    console.error("Error checking Donna status", error);
    setEnableDona(false);
  }
};


