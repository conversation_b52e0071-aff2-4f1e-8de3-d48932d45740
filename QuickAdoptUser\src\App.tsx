import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom'; // Import BrowserRouter
import ChatBot from './components/ChatBot';
import WorkerAgent from './components/workeragent/WorkerAgent';
import workAgentSignalRService, { WorkAgentMessage } from './services/SignalRService';
import { GuideDetailsProvider } from "./context/GuideDetailsContext";
import Announcement from "./components/Announcement/Announcement";
import Banner from "./components/Banner/Banner";
import Hotspotview from './components/Hotspot/Hotspotview';
import TooltipUserview from './components/Tooltips/Tooltipuserview';
import TourUserView from './components/Tours/TourUserView';
import ChecklistLauncher from './components/Checklist/ChecklistLauncher';

import { donaEnabled } from './service/APIService';
import { SnackbarProvider } from './hooks/SnackbarContext';
import { IsDonnaEnabled } from './components/Services/AssistantService';

const App = () => {
  const [isFromAi, setIsFromAi] = useState(false);
  const [guide, setGuide] = useState<any>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [showFeedbackPopup, setShowFeedbackPopup] = useState(false);
  const [feedbackInfoState, setFeedbackInfoState] = useState<boolean | null>(null); // <-- Add this line
  const location =window.location.href;
  const hideChatBotPaths = ".quickadopt.in/settings/install";
  const shouldHideChatBot = location.includes(hideChatBotPaths);
  const [isOpenAiKeyProvided, setOpenAiKeyProvided] = useState("");
  const[submitClicked,setSubmitClicked]=useState(false);

  

	const [workerAgentVisible, setWorkerAgentVisible] = useState(false); // Start hidden
  const [startTraining, setStartTraining] = useState(false);
  const [backgroundMode, setBackgroundMode] = useState(false);
  // bindingDataReady state removed - worker agent starts immediately when data is received
  const [bindingData, setBindingData] = useState<Array<{labelName: string; value: any; xpath: string; type: string; key: string; name: string; selector: string; cssSelector: string;}>>([]);

  // Get SignalR service instance
  const signalRService = workAgentSignalRService;
  const[agentId,setAgentId]=useState<string>("");


  // Training button functionality removed - worker agent starts automatically

  // Worker agent starts automatically when binding data is received via SignalR callback

  // Set up SignalR callbacks when component mounts
  useEffect(() => {
    const callbacks = {
      onMessage: (message: WorkAgentMessage | string) => {
        console.log('SignalR Message:', message);
      },
      onError: (error: string) => {
        console.error('SignalR Error:', error);
      },
      onBindingDataReady: (data: Array<{labelName: string; value: any; xpath: string; type: string; key: string;name:string;selector:string;cssSelector:string;
      }>) => {
        console.log('?? Binding data ready received in App.tsx:', data);
        console.log('?? Binding data length:', data.length);

        // Immediately start worker agent when binding data is received
        console.log('?? Starting worker agent automatically with binding data...');
        setBindingData(data);
        setBackgroundMode(true); // Set background mode
        setWorkerAgentVisible(false); // Hide the UI from user
        setStartTraining(true); // Start the worker agent immediately

        console.log('?? Worker agent started automatically');
      },
      onConnectionEstablished: (message: string) => {
        console.log('SignalR Connection Established:', message);
      },
      onConnectionStateChanged: (state: any) => {
        console.log('SignalR Connection State Changed:', state);
      }
    };

    // Ensure connection and add callbacks
    // signalRService.ensureConnection().then(() => {
    //   signalRService.addCallbacks(callbacks);
    // }).catch((error:any) => {
    //   console.error('Failed to establish SignalR connection:', error);
    // });

    // Cleanup on unmount
    return () => {
      signalRService.removeCallbacks(callbacks);
    };
  }, []);

	const [enableDonna, setEnableDona] = useState(false);
  const accountId = localStorage.getItem("AccountId") || "";
  const domainAccountId = localStorage.getItem("DomainAccountId") || "";

  useEffect(() => {
    IsDonnaEnabled(accountId, domainAccountId, setEnableDona);
  }, []);


  return (
    
    <BrowserRouter> {/* Wrap your app with BrowserRouter */}
		<SnackbarProvider>
      <GuideDetailsProvider>
        <div>
          <Banner />
          <Announcement />
          <Hotspotview />
          <TooltipUserview />
          <TourUserView
            guide={guide}
            isFromAi={isFromAi}
            setGuide={setGuide}
            setIsFromAi={setIsFromAi}
            isOpen={isOpen}
            setIsOpen={setIsOpen}
            showFeedbackPopup={showFeedbackPopup}
            setShowFeedbackPopup={setShowFeedbackPopup}
            feedbackInfoState={feedbackInfoState} // <-- Pass down
            setFeedbackInfoState={setFeedbackInfoState} // <-- Pass down
          />
          <ChecklistLauncher />
          { (enableDonna == true) && !shouldHideChatBot && (
            <ChatBot
              guide={guide}
              isFromAi={isFromAi}
              setGuide={setGuide}
              setIsFromAi={setIsFromAi}
              isOpen={isOpen}
              setIsOpen={setIsOpen}
              showFeedbackPopup={showFeedbackPopup}
              setShowFeedbackPopup={setShowFeedbackPopup}
              feedbackInfoState={feedbackInfoState} // <-- Pass down
              setFeedbackInfoState={setFeedbackInfoState}
              onStartTraining={() => {}}
              setStartTraining={setStartTraining}
              setBackgroundMode={setBackgroundMode}
              setBindingData={setBindingData}
              setWorkerAgentVisible={setWorkerAgentVisible}
              agentId={agentId}
              setAgentId={setAgentId}
                isOpenAiKeyProvided={isOpenAiKeyProvided}
              setOpenAiKeyProvided={setOpenAiKeyProvided}
               />)}
               <WorkerAgent
          isVisible={workerAgentVisible}
          onToggleVisibility={() => setWorkerAgentVisible(!workerAgentVisible)}
          startTraining={startTraining}
          onTrainingStarted={() => {
            setStartTraining(false);
            setBackgroundMode(false);
            setBindingData([]);
          }}
          backgroundMode={backgroundMode}
          bindingData={bindingData} // Pass binding data to WorkerAgent
            setAgentId={setAgentId}
          agentId={agentId}

          setBindingData={setBindingData} // Pass setBindingData prop to WorkerAgent
          setBackgroundMode={setBackgroundMode} // Pass setBackgroundMode prop to WorkerAgent
           setStartTraining={setStartTraining}
           setWorkerAgentVisible={setWorkerAgentVisible}
           setSubmitClicked={setSubmitClicked}
           submitClicked={submitClicked}
            />
      
        </div>
      </GuideDetailsProvider>
		</SnackbarProvider>
    </BrowserRouter>
  );
};

export default App;

