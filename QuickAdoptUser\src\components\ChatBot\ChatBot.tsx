import React, { useState, useEffect } from 'react';
import Chat<PERSON>utton, { ChatDisplayMode } from './ChatButton';
import ChatModal from './ChatModal';
import { createNewThread } from '../Services/AssistantService';
import { preloadVoices } from '../../services/TextToSpeechService';
import TourUserView from '../Tours/TourUserView'; // Import TourUserView
import CustomAgentChatModal from './CustomAgentChatModal';


interface Message {
  text: string;
  isUser: boolean;
  timestamp: Date;
  id?: string; // Unique identifier for each message
  guide: any;
  isStatusMessage?: boolean;
 // Flag for status messages
}

const ChatBot: React.FC<any> = ({ guide, setGuide, isFromAi, setIsFromAi, isOpen, setIsOpen, showFeedbackPopup, setShowFeedbackPopup, feedbackInfoState, setFeedbackInfoState,isOpenAiKeyProvided,setOpenAiKeyProvided,onStartTraining,setWorkerAgentVisible,setStartTraining, setBackgroundMode,setBindingData,agentId,setAgentId}: any) => {

  const [voicesLoaded, setVoicesLoaded] = useState(false);
  const [newThreadCreated, setNewThreadCreated] = useState(false);
  const [isWelcomeMessageShown, setWelcomeMessageShown] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isReWelcomeMessageShown, setReWelcomeMessageShown] = useState(false);
  const [displayMode, setDisplayMode] = useState<ChatDisplayMode>('sidePanel');

  // Preload ResponsiveVoice when component mounts
  const handleOpenChat = async () => {
    try {
      setIsOpen(true);

    } catch (error) {
      console.error("Failed to create thread:", error);
    }
  };


  const handleCloseChat = () => {
    setIsOpen(false);
  };

  const handleModeSelect = async (mode: ChatDisplayMode) => {
    try {
      setDisplayMode(mode);
      setIsOpen(true);

    } catch (error) {
      console.error("Failed to create thread:", error);
    }
  };

  

  return (
    <>
      {!isOpen ?
        <ChatButton onModeSelect={handleModeSelect} />:
        <>
          <ChatModal
            open={isOpen}
            onClose={handleCloseChat}
            guide={guide}
            isFromAi={isFromAi}
            setGuide={setGuide}
            setIsFromAi={setIsFromAi}
            setIsOpen={setIsOpen}
            newThreadCreated={newThreadCreated}
            setNewThreadCreated={setNewThreadCreated}
            setWelcomeMessageShown={setWelcomeMessageShown}
            isWelcomeMessageShown={isWelcomeMessageShown}
            messages={messages}
            setMessages={setMessages}
            isReWelcomeMessageShown={isReWelcomeMessageShown}
            setReWelcomeMessageShown={setReWelcomeMessageShown}
            setShowFeedbackPopup={setShowFeedbackPopup}
            feedbackInfoState={feedbackInfoState} // Pass down
            setFeedbackInfoState={setFeedbackInfoState} // Pass down
			isOpenAiKeyProvided={isOpenAiKeyProvided}
            setOpenAiKeyProvided={setOpenAiKeyProvided}
 			onStartTraining={onStartTraining}
          setStartTraining={setStartTraining}
          setBackgroundMode={setBackgroundMode}
          setBindingData={setBindingData}
          setWorkerAgentVisible={setWorkerAgentVisible}
           setAgentId={setAgentId}
            agentId={agentId}
            displayMode={displayMode}
          />
        </>
      }
    </>
  );
};

export default ChatBot;
